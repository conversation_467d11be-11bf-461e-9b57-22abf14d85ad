"""
Comprehensive Diagnostic and Activation Script
Identifies all issues, bottlenecks, and missing dependencies before activation
"""

import sys
import os
import subprocess
import importlib
import time
import json
import logging
from pathlib import Path

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class VoiceAgentDiagnostic:
    def __init__(self):
        self.issues = []
        self.warnings = []
        self.recommendations = []
        self.missing_deps = []
        self.performance_bottlenecks = []
        
    def check_python_version(self):
        """Check Python version compatibility"""
        print("🐍 Checking Python Version...")
        version = sys.version_info
        if version.major < 3 or (version.major == 3 and version.minor < 8):
            self.issues.append(f"Python {version.major}.{version.minor} detected. Requires Python 3.8+")
            return False
        print(f"   ✓ Python {version.major}.{version.minor}.{version.micro} - Compatible")
        return True
    
    def check_critical_dependencies(self):
        """Check all critical dependencies"""
        print("\n📦 Checking Critical Dependencies...")
        
        critical_deps = {
            'torch': 'PyTorch (AI/GPU acceleration)',
            'transformers': 'Hugging Face Transformers (TTS)',
            'faster_whisper': 'Faster-Whisper (STT)',
            'soundfile': 'SoundFile (Audio I/O)',
            'numpy': 'NumPy (Array processing)',
            'requests': 'Requests (API calls)',
            'pyaudio': 'PyAudio (Audio devices)',
            'webrtcvad': 'WebRTC VAD (Voice detection)',
            'librosa': 'Librosa (Audio processing)',
            'scipy': 'SciPy (Signal processing)',
            'datasets': 'HF Datasets (Model loading)',
            'accelerate': 'HF Accelerate (GPU optimization)',
            'sentencepiece': 'SentencePiece (Tokenization)',
            'ctranslate2': 'CTranslate2 (Fast inference)'
        }
        
        available = {}
        for module, description in critical_deps.items():
            try:
                importlib.import_module(module)
                available[module] = True
                print(f"   ✓ {module} - {description}")
            except ImportError:
                available[module] = False
                self.missing_deps.append(f"{module} - {description}")
                print(f"   ✗ {module} - {description} - MISSING")
        
        return available
    
    def check_gpu_setup(self):
        """Check GPU and CUDA setup"""
        print("\n🚀 Checking GPU Setup...")
        
        try:
            import torch
            cuda_available = torch.cuda.is_available()
            device_count = torch.cuda.device_count()
            
            if cuda_available:
                print(f"   ✓ CUDA Available - {device_count} GPU(s)")
                for i in range(device_count):
                    gpu_name = torch.cuda.get_device_name(i)
                    memory = torch.cuda.get_device_properties(i).total_memory / 1024**3
                    print(f"     GPU {i}: {gpu_name} ({memory:.1f} GB)")
                return True
            else:
                print("   ⚠ CUDA Not Available - Will use CPU (slower)")
                self.warnings.append("GPU acceleration not available - performance will be limited")
                self.performance_bottlenecks.append("CPU-only processing will increase latency")
                return False
        except Exception as e:
            print(f"   ✗ GPU Check Failed: {e}")
            self.issues.append(f"GPU check failed: {e}")
            return False
    
    def check_audio_devices(self):
        """Check audio input/output devices"""
        print("\n🎤 Checking Audio Devices...")
        
        try:
            import pyaudio
            p = pyaudio.PyAudio()
            
            # Check input devices
            input_devices = []
            output_devices = []
            
            for i in range(p.get_device_count()):
                info = p.get_device_info_by_index(i)
                if info['maxInputChannels'] > 0:
                    input_devices.append(f"Input {i}: {info['name']}")
                if info['maxOutputChannels'] > 0:
                    output_devices.append(f"Output {i}: {info['name']}")
            
            p.terminate()
            
            if input_devices:
                print(f"   ✓ Found {len(input_devices)} input device(s)")
                for device in input_devices[:3]:  # Show first 3
                    print(f"     {device}")
            else:
                self.issues.append("No audio input devices found")
                
            if output_devices:
                print(f"   ✓ Found {len(output_devices)} output device(s)")
                for device in output_devices[:3]:  # Show first 3
                    print(f"     {device}")
            else:
                self.issues.append("No audio output devices found")
                
            return len(input_devices) > 0 and len(output_devices) > 0
            
        except Exception as e:
            print(f"   ✗ Audio Device Check Failed: {e}")
            self.issues.append(f"Audio device check failed: {e}")
            return False
    
    def check_ollama_connection(self):
        """Check Ollama connection and models"""
        print("\n🤖 Checking Ollama Connection...")
        
        try:
            import requests
            
            # Test Ollama connection
            response = requests.get("http://localhost:11434/api/tags", timeout=5)
            if response.status_code == 200:
                models = response.json().get('models', [])
                print(f"   ✓ Ollama Connected - {len(models)} model(s) available")
                
                # Check for required model
                qwen_models = [m for m in models if 'qwen2.5vl' in m.get('name', '').lower()]
                if qwen_models:
                    for model in qwen_models:
                        print(f"     ✓ {model['name']} - Available")
                else:
                    self.issues.append("Qwen2.5VL model not found in Ollama")
                    print("     ✗ qwen2.5vl:7b - Not found")
                    
                return True
            else:
                self.issues.append(f"Ollama connection failed: HTTP {response.status_code}")
                return False
                
        except requests.exceptions.ConnectionError:
            self.issues.append("Ollama not running - start with 'ollama serve'")
            print("   ✗ Ollama Not Running")
            return False
        except Exception as e:
            self.issues.append(f"Ollama check failed: {e}")
            print(f"   ✗ Ollama Check Failed: {e}")
            return False
    
    def check_configuration(self):
        """Check voice agent configuration"""
        print("\n⚙️ Checking Configuration...")
        
        try:
            config_path = Path("voice_agent_config.json")
            if not config_path.exists():
                self.issues.append("Configuration file missing: voice_agent_config.json")
                return False
            
            with open(config_path) as f:
                config = json.load(f)
            
            # Check critical settings
            critical_settings = {
                'target_latency_ms': 'Target latency',
                'stt_model_size': 'STT model size',
                'qwen_model': 'AI model',
                'tts_model_name': 'TTS model',
                'input_chunk_size': 'Buffer size'
            }
            
            for setting, description in critical_settings.items():
                if setting in config:
                    print(f"   ✓ {description}: {config[setting]}")
                else:
                    self.warnings.append(f"Missing config: {setting}")
            
            # Check for performance bottlenecks in config
            if config.get('input_chunk_size', 1024) > 512:
                self.performance_bottlenecks.append("Large buffer size may increase latency")
            
            if config.get('target_latency_ms', 800) > 600:
                self.performance_bottlenecks.append("High latency target - consider reducing")
            
            return True
            
        except Exception as e:
            self.issues.append(f"Configuration check failed: {e}")
            return False
    
    def check_model_availability(self):
        """Check if required models are available"""
        print("\n🧠 Checking Model Availability...")
        
        # Check Whisper models
        try:
            from faster_whisper import WhisperModel
            print("   ✓ Faster-Whisper available")
            
            # Try to load tiny model
            try:
                model = WhisperModel("tiny", device="cpu")
                print("   ✓ Whisper tiny model - Available")
                del model  # Free memory
            except Exception as e:
                self.warnings.append(f"Whisper tiny model issue: {e}")
                
        except ImportError:
            self.missing_deps.append("faster-whisper")
        
        # Check HuggingFace models
        try:
            from transformers import pipeline
            print("   ✓ Transformers available")
        except ImportError:
            self.missing_deps.append("transformers")
    
    def identify_bottlenecks(self):
        """Identify potential performance bottlenecks"""
        print("\n⚡ Identifying Performance Bottlenecks...")
        
        # CPU vs GPU
        try:
            import torch
            if not torch.cuda.is_available():
                self.performance_bottlenecks.append("CPU-only processing (no CUDA)")
        except:
            pass
        
        # Memory constraints
        try:
            import psutil
            memory = psutil.virtual_memory()
            if memory.available < 4 * 1024**3:  # Less than 4GB
                self.performance_bottlenecks.append(f"Low available RAM: {memory.available/1024**3:.1f}GB")
        except:
            pass
        
        # Print bottlenecks
        if self.performance_bottlenecks:
            for bottleneck in self.performance_bottlenecks:
                print(f"   ⚠ {bottleneck}")
        else:
            print("   ✓ No major bottlenecks detected")
    
    def generate_installation_commands(self):
        """Generate commands to fix missing dependencies"""
        if not self.missing_deps:
            return []
        
        print("\n🔧 Installation Commands Needed:")
        commands = []
        
        # Group by installation method
        pip_packages = []
        special_installs = []
        
        for dep in self.missing_deps:
            package = dep.split(' - ')[0]
            if package in ['pyaudio']:
                special_installs.append(f"# {package} may need system libraries")
                special_installs.append(f"pip install {package}")
            elif package in ['torch']:
                special_installs.append("# PyTorch - choose CPU or CUDA version")
                special_installs.append("pip install torch torchvision torchaudio")
            else:
                pip_packages.append(package)
        
        if pip_packages:
            cmd = f"pip install {' '.join(pip_packages)}"
            commands.append(cmd)
            print(f"   {cmd}")
        
        for cmd in special_installs:
            commands.append(cmd)
            print(f"   {cmd}")
        
        return commands
    
    def generate_report(self):
        """Generate comprehensive diagnostic report"""
        print("\n" + "="*60)
        print("🔍 VOICE AGENT DIAGNOSTIC REPORT")
        print("="*60)
        
        # Issues
        if self.issues:
            print("\n❌ CRITICAL ISSUES (Must Fix):")
            for i, issue in enumerate(self.issues, 1):
                print(f"   {i}. {issue}")
        
        # Warnings
        if self.warnings:
            print("\n⚠️ WARNINGS (Recommended to Fix):")
            for i, warning in enumerate(self.warnings, 1):
                print(f"   {i}. {warning}")
        
        # Performance Bottlenecks
        if self.performance_bottlenecks:
            print("\n⚡ PERFORMANCE BOTTLENECKS:")
            for i, bottleneck in enumerate(self.performance_bottlenecks, 1):
                print(f"   {i}. {bottleneck}")
        
        # Installation commands
        commands = self.generate_installation_commands()
        
        # Overall status
        critical_issues = len(self.issues)
        if critical_issues == 0:
            print(f"\n✅ SYSTEM STATUS: READY TO ACTIVATE")
            print("   No critical issues found!")
        else:
            print(f"\n🚫 SYSTEM STATUS: {critical_issues} CRITICAL ISSUE(S)")
            print("   Fix critical issues before activation")
        
        return critical_issues == 0

def main():
    """Run comprehensive diagnostic"""
    print("🔍 VOICE AGENT COMPREHENSIVE DIAGNOSTIC")
    print("Checking for issues, bottlenecks, and missing dependencies...")
    print("="*60)
    
    diagnostic = VoiceAgentDiagnostic()
    
    # Run all checks
    checks = [
        diagnostic.check_python_version(),
        diagnostic.check_critical_dependencies(),
        diagnostic.check_gpu_setup(),
        diagnostic.check_audio_devices(),
        diagnostic.check_ollama_connection(),
        diagnostic.check_configuration(),
        diagnostic.check_model_availability()
    ]
    
    # Identify bottlenecks
    diagnostic.identify_bottlenecks()
    
    # Generate report
    ready = diagnostic.generate_report()
    
    if ready:
        print("\n🚀 READY TO ACTIVATE!")
        print("Run: python ultra_voice_agent.py")
    else:
        print("\n🔧 FIX ISSUES FIRST, THEN ACTIVATE")

if __name__ == "__main__":
    main()
