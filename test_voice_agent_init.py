"""
Test Voice Agent Initialization
Quick test to verify the main voice agent can initialize with all optimizations
"""

import sys
import os
import logging
import time

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from ultra_voice_agent import UltraVoiceAgent
from core.config import VoiceAgentConfig

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_voice_agent_initialization():
    """Test complete voice agent initialization"""
    print("=== Testing Voice Agent Initialization ===")
    
    try:
        # Load optimized configuration
        print("Loading optimized configuration...")
        config = VoiceAgentConfig.load_from_file('voice_agent_config.json')
        print("  Configuration: ✓ Loaded")
        
        # Create voice agent
        print("Creating voice agent...")
        agent = UltraVoiceAgent(config)
        print("  Voice Agent: ✓ Created")
        
        # Test initialization
        print("Initializing voice agent...")
        start_time = time.time()
        
        if agent.initialize():
            init_time = time.time() - start_time
            print(f"  Initialization: ✓ Success ({init_time:.2f}s)")
            
            # Get performance stats
            stats = agent.get_performance_stats()
            print(f"  Performance Stats Available: {'✓' if stats else '✗'}")

            # Test component status
            components = [
                'audio_input_thread', 'vad_thread', 'stt_thread',
                'ai_inference_thread', 'tts_thread', 'audio_output_thread'
            ]

            print("  Component Status:")
            for component in components:
                if hasattr(agent, component):
                    thread = getattr(agent, component)
                    status = "✓ Available" if thread else "✗ Missing"
                    print(f"    {component.replace('_thread', '')}: {status}")
                else:
                    print(f"    {component.replace('_thread', '')}: ✗ Missing")
            
            # Cleanup
            print("Cleaning up...")
            agent.cleanup()
            print("  Cleanup: ✓ Complete")
            
            return True
        else:
            print("  Initialization: ✗ Failed")
            return False
            
    except Exception as e:
        print(f"  Voice Agent Test: ✗ Exception: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_performance_metrics():
    """Test performance monitoring"""
    print("\n=== Testing Performance Metrics ===")
    
    try:
        config = VoiceAgentConfig.load_from_file('voice_agent_config.json')
        agent = UltraVoiceAgent(config)
        
        if agent.initialize():
            # Get performance report
            try:
                report = agent.get_performance_report()
                print("  Performance Report: ✓ Available")
                print(f"    Target Latency: {config.target_latency_ms}ms")
                print(f"    Buffer Size: {config.input_chunk_size}")
                print(f"    Mixed Precision: {config.enable_mixed_precision}")
                print(f"    Model Quantization: {config.enable_model_quantization}")
            except Exception as e:
                print(f"  Performance Report: ✗ Error: {e}")
            
            agent.cleanup()
            return True
        else:
            return False
            
    except Exception as e:
        print(f"  Performance Test: ✗ Exception: {e}")
        return False

def generate_initialization_report(results):
    """Generate initialization test report"""
    print("\n" + "="*50)
    print("VOICE AGENT INITIALIZATION TEST REPORT")
    print("="*50)
    
    # Test results summary
    print("Initialization Test Results:")
    for test_name, result in results.items():
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"  {test_name}: {status}")
    
    # Overall status
    all_passed = all(results.values())
    print(f"\nOverall Status: {'✓ INITIALIZATION SUCCESSFUL' if all_passed else '⚠ INITIALIZATION ISSUES'}")
    
    if all_passed:
        print("\n🎉 Voice Agent is ready for use!")
        print("   - All components initialized successfully")
        print("   - Optimizations are active")
        print("   - System is ready for voice conversations")
        print("\nNext steps:")
        print("   - Run 'python start_conversation.py' to start talking")
        print("   - Or use 'python ultra_voice_agent.py' for advanced options")
    else:
        print("\n⚠ Some components failed to initialize.")
        print("   Please check the error messages above.")

def main():
    """Run voice agent initialization test"""
    print("Voice Agent Initialization Test")
    print("Testing complete system initialization with optimizations")
    print("="*50)
    
    # Run tests
    results = {}
    
    # Test voice agent initialization
    results['Voice Agent Initialization'] = test_voice_agent_initialization()
    
    # Test performance metrics
    results['Performance Metrics'] = test_performance_metrics()
    
    # Generate report
    generate_initialization_report(results)

if __name__ == "__main__":
    main()
