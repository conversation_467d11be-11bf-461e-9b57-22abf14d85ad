"""
Test Optimized Voice Agent System
Comprehensive test for all optimizations including GPU acceleration, streaming, and conversation logic
"""

import sys
import os
import logging
import time
import json
import torch

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.config import VoiceAgentConfig
from core.huggingface_tts_processor import HuggingFaceTTSThread, HUGGINGFACE_AVAILABLE
from core.stt_processor import STTThread, FASTER_WHISPER_AVAILABLE
from core.ai_inference import AIInferenceThread
from core.threading_infrastructure import LockFreeQueue, ProcessingResult

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_gpu_availability():
    """Test GPU availability and CUDA setup"""
    print("=== Testing GPU Availability ===")
    
    results = {}
    
    # Test PyTorch CUDA
    cuda_available = torch.cuda.is_available()
    device_count = torch.cuda.device_count()
    
    print(f"  CUDA Available: {'✓' if cuda_available else '✗'} {cuda_available}")
    print(f"  GPU Device Count: {device_count}")
    
    if cuda_available:
        for i in range(device_count):
            gpu_name = torch.cuda.get_device_name(i)
            gpu_memory = torch.cuda.get_device_properties(i).total_memory / 1024**3
            print(f"    GPU {i}: {gpu_name} ({gpu_memory:.1f} GB)")
    
    results['cuda_available'] = cuda_available
    results['device_count'] = device_count
    
    return results

def test_optimized_configuration():
    """Test the optimized configuration"""
    print("\n=== Testing Optimized Configuration ===")
    
    try:
        # Load the optimized configuration
        config = VoiceAgentConfig.load_from_file('voice_agent_config.json')
        
        print("  Configuration loaded: ✓ Success")
        
        # Check key optimizations
        optimizations = {
            'Small STT Model': config.stt_model_size == 'tiny',
            'GPU STT': config.stt_device == 'cuda',
            'Small AI Model': '7b' in config.qwen_model,
            'GPU TTS': config.tts_device == 'cuda',
            'Reduced Latency Target': config.target_latency_ms <= 600,
            'Small Buffer Sizes': config.input_chunk_size <= 512,
            'Mixed Precision': config.enable_mixed_precision,
            'Model Quantization': config.enable_model_quantization
        }
        
        for opt_name, enabled in optimizations.items():
            status = "✓" if enabled else "✗"
            print(f"    {opt_name}: {status} {enabled}")
        
        all_optimized = all(optimizations.values())
        print(f"  All Optimizations: {'✓ Enabled' if all_optimized else '⚠ Some Missing'}")
        
        return config, all_optimized
        
    except Exception as e:
        print(f"  Configuration test: ✗ Failed: {e}")
        return None, False

def test_stt_performance():
    """Test STT performance with optimizations"""
    print("\n=== Testing STT Performance ===")
    
    if not FASTER_WHISPER_AVAILABLE:
        print("  Faster-Whisper: ✗ Not available")
        return False
    
    try:
        # Create test queues
        input_queue = LockFreeQueue(maxsize=100, name="STTTestInput")
        output_queue = LockFreeQueue(maxsize=100, name="STTTestOutput")
        
        # Create STT thread with optimized settings
        stt = STTThread(
            input_queue=input_queue,
            output_queue=output_queue,
            model_size="tiny",  # Optimized model
            device="cuda" if torch.cuda.is_available() else "cpu",
            language="en"
        )
        
        print("  STT Thread creation: ✓ Success")
        
        # Test initialization
        if stt.initialize():
            print("  STT Initialization: ✓ Success")
            
            # Get model info
            stats = stt.get_stt_stats()
            print(f"    Model: {stats['model_size']}")
            print(f"    Device: {stats['device']}")
            print(f"    CUDA Available: {stats.get('cuda_available', False)}")
            
            stt.cleanup()
            return True
        else:
            print("  STT Initialization: ✗ Failed")
            return False
            
    except Exception as e:
        print(f"  STT Test: ✗ Exception: {e}")
        return False

def test_tts_streaming():
    """Test TTS streaming capabilities"""
    print("\n=== Testing TTS Streaming ===")
    
    if not HUGGINGFACE_AVAILABLE:
        print("  Hugging Face TTS: ✗ Not available")
        return False
    
    try:
        # Create test queues
        input_queue = LockFreeQueue(maxsize=100, name="TTSStreamTestInput")
        output_queue = LockFreeQueue(maxsize=100, name="TTSStreamTestOutput")
        
        # Create TTS thread with streaming enabled
        tts = HuggingFaceTTSThread(
            input_queue=input_queue,
            output_queue=output_queue,
            model_name="facebook/mms-tts-eng",  # Working model
            device="cuda" if torch.cuda.is_available() else "cpu",
            voice_preset="female",
            speech_rate=1.4,  # Optimized rate
            volume=0.9
        )
        
        print("  TTS Thread creation: ✓ Success")
        
        # Test initialization
        if tts.initialize():
            print("  TTS Initialization: ✓ Success")
            
            # Test streaming with long text
            long_text = "This is a comprehensive test of the streaming text-to-speech system. The system should be able to process this longer text efficiently by breaking it into smaller chunks and processing them in a streaming fashion to reduce overall latency."
            
            test_result = ProcessingResult(
                data={'text': long_text},
                timestamp=time.time(),
                processing_time_ms=0,
                metadata={'source': 'streaming_test'}
            )
            
            start_time = time.time()
            audio_result = tts._synthesize_speech(test_result)
            synthesis_time = time.time() - start_time
            
            if audio_result:
                is_streaming = audio_result.data.get('is_streaming', False)
                chunk_info = f"chunk {audio_result.data.get('chunk_index', 0)}/{audio_result.data.get('total_chunks', 1)}" if is_streaming else "standard"
                
                print(f"  TTS Streaming: ✓ Success ({chunk_info})")
                print(f"    - Synthesis time: {synthesis_time:.2f}s")
                print(f"    - Streaming mode: {'✓' if is_streaming else '✗'}")
                print(f"    - Text length: {len(long_text)} chars")
                
                tts.cleanup()
                return True
            else:
                print("  TTS Streaming: ✗ Failed")
                tts.cleanup()
                return False
        else:
            print("  TTS Initialization: ✗ Failed")
            return False
            
    except Exception as e:
        print(f"  TTS Streaming Test: ✗ Exception: {e}")
        return False

def test_conversation_logic():
    """Test conversation logic improvements"""
    print("\n=== Testing Conversation Logic ===")
    
    try:
        # Create test queues
        input_queue = LockFreeQueue(maxsize=100, name="AITestInput")
        output_queue = LockFreeQueue(maxsize=100, name="AITestOutput")
        
        # Create AI inference thread
        ai = AIInferenceThread(
            input_queue=input_queue,
            output_queue=output_queue,
            ai_engine="ollama",
            model_name="qwen2.5vl:7b",  # Optimized model
            context_memory_size=20
        )
        
        print("  AI Thread creation: ✓ Success")
        
        # Test repetitive input detection
        test_text = "Hello there"
        current_time = time.time()
        
        # Simulate repetitive input
        ai.last_user_input = test_text
        ai.last_user_input_time = current_time - 1.0  # 1 second ago
        
        is_repetitive = ai._is_repetitive_input(test_text, current_time)
        print(f"  Repetitive Input Detection: {'✓' if is_repetitive else '✗'} {is_repetitive}")
        
        # Test text similarity
        similarity = ai._calculate_text_similarity("Hello there", "Hello there")
        print(f"  Text Similarity (identical): {similarity:.2f}")
        
        similarity2 = ai._calculate_text_similarity("Hello there", "Hi there")
        print(f"  Text Similarity (similar): {similarity2:.2f}")
        
        print("  Conversation Logic: ✓ Success")
        return True
        
    except Exception as e:
        print(f"  Conversation Logic Test: ✗ Exception: {e}")
        return False

def generate_optimization_report(results):
    """Generate comprehensive optimization report"""
    print("\n" + "="*60)
    print("VOICE AGENT OPTIMIZATION TEST REPORT")
    print("="*60)
    
    # System info
    print(f"PyTorch version: {torch.__version__}")
    print(f"CUDA available: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"GPU: {torch.cuda.get_device_name(0)}")
    
    # Test results summary
    print("\nOptimization Test Results:")
    for test_name, result in results.items():
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"  {test_name}: {status}")
    
    # Overall status
    all_passed = all(results.values())
    print(f"\nOverall Status: {'✓ ALL OPTIMIZATIONS WORKING' if all_passed else '⚠ SOME ISSUES DETECTED'}")
    
    if all_passed:
        print("\n🚀 Voice Agent optimizations verified successfully!")
        print("   - GPU acceleration configured")
        print("   - Streaming architecture implemented")
        print("   - Conversation logic improved")
        print("   - Performance targets achieved")
    else:
        print("\n⚠ Some optimizations need attention. Please review failed tests.")

def main():
    """Run comprehensive optimization test"""
    print("Voice Agent Optimization Test")
    print("Testing all performance improvements and optimizations")
    print("="*60)
    
    # Run all tests
    results = {}
    
    # Test GPU availability
    gpu_results = test_gpu_availability()
    results['GPU Setup'] = gpu_results['cuda_available']
    
    # Test optimized configuration
    config, config_optimized = test_optimized_configuration()
    results['Optimized Configuration'] = config_optimized
    
    # Test STT performance
    results['STT Performance'] = test_stt_performance()
    
    # Test TTS streaming
    results['TTS Streaming'] = test_tts_streaming()
    
    # Test conversation logic
    results['Conversation Logic'] = test_conversation_logic()
    
    # Generate report
    generate_optimization_report(results)

if __name__ == "__main__":
    main()
