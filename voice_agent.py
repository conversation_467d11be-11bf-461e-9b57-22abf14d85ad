"""
Main application for the AI Voice Agent.
"""

import logging
import time

from core.config import VoiceAgentConfig
from core.audio_input import AudioInputThread
from core.audio_output import AudioOutput
from core.tts_processor import TTSProcessor
from core.exceptions import AudioIOError

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class VoiceAgent:
    """The main class for the AI Voice Agent."""

    def __init__(self):
        """Initialize the voice agent."""
        try:
            self.config = VoiceAgentConfig.load_from_file('voice_agent_config.json')
        except FileNotFoundError:
            logger.info("Configuration file not found, creating a default one.")
            self.config = VoiceAgentConfig()
            self.config.save_to_file('voice_agent_config.json')

        self.audio_input = AudioInputThread(self.config)
        self.audio_output = AudioOutput(self.config)
        self.tts_processor = TTSProcessor(self.config)

    def run(self):
        """Run the main loop of the voice agent."""
        logger.info("Starting AI Voice Agent...")
        try:
            with self.audio_input as mic, self.audio_output as speakers:
                logger.info("Voice agent is running. Press Ctrl+C to exit.")
                while True:
                    try:
                        audio_chunk = mic.read_chunk()
                        # TODO: Process the audio chunk (VAD, STT, AI)
                        
                        # For testing TTS, let's make it say something.
                        # This will be replaced by the AI's response.
                        self.tts_processor.text_to_speech("Hello, I am your new AI assistant.")

                        # For now, we still echo the input
                        speakers.write_chunk(audio_chunk)
                    except AudioIOError as e:
                        logger.error(f"An audio error occurred: {e}")
                        break
                    except KeyboardInterrupt:
                        break
        except AudioIOError as e:
            logger.critical(f"Failed to initialize audio devices: {e}")
        finally:
            logger.info("Shutting down AI Voice Agent.")

if __name__ == "__main__":
    agent = VoiceAgent()
    agent.run()