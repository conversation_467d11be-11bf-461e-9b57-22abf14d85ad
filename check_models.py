import requests
import json

try:
    response = requests.get("http://localhost:11434/api/tags", timeout=5)
    if response.status_code == 200:
        data = response.json()
        models = data.get('models', [])
        
        print(f"Available Ollama Models ({len(models)}):")
        print("="*50)
        
        qwen_models = []
        for model in models:
            name = model.get('name', '')
            size = model.get('size', 0) / (1024**3)  # Convert to GB
            print(f"  {name} ({size:.1f} GB)")
            
            if 'qwen' in name.lower():
                qwen_models.append(name)
        
        print(f"\nQwen Models Found: {len(qwen_models)}")
        for model in qwen_models:
            print(f"  ✓ {model}")
            
        if 'qwen2.5vl:7b' not in [m.lower() for m in qwen_models]:
            print(f"\n⚠️  qwen2.5vl:7b not found!")
            if qwen_models:
                print(f"   Available alternative: {qwen_models[0]}")
            else:
                print("   No Qwen models available")
    else:
        print(f"Error: HTTP {response.status_code}")
        
except Exception as e:
    print(f"Error checking models: {e}")
