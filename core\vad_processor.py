"""
Voice Activity Detection (VAD) Processor
High-performance voice activity detection with multiple algorithms
"""

import numpy as np
import time
import logging
from typing import Optional, Dict, Any, List, Tuple
import threading
from collections import deque

try:
    import torch
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False

from .threading_infrastructure import (
    HighPriorityThread,
    LockFreeQueue,
    ProcessingResult
)

logger = logging.getLogger(__name__)


class VADThread(HighPriorityThread):
    """Voice Activity Detection thread"""
    
    def __init__(self,
                 input_queue: LockFreeQueue,
                 output_queue: LockFreeQueue,
                 method: str = "custom",
                 sensitivity: float = 0.3,
                 min_speech_duration: float = 0.15,
                 max_silence_duration: float = 0.8,
                 sample_rate: int = 48000):
        
        super().__init__("VAD", priority="high", target_fps=100)
        
        # Queues
        self.input_queue = input_queue
        self.output_queue = output_queue
        
        # VAD configuration
        self.method = method
        self.sensitivity = sensitivity
        self.min_speech_duration = min_speech_duration
        self.max_silence_duration = max_silence_duration
        self.sample_rate = sample_rate
        
        # VAD state
        self.is_speaking = False
        self.speech_start_time = 0
        self.silence_start_time = 0
        self.last_speech_time = 0
        
        # Audio buffer for speech collection
        self.speech_buffer = deque(maxlen=int(sample_rate * 10))  # 10 second buffer
        self.current_speech_frames = []
        
        # VAD models/processors
        self.silero_model = None
        self.webrtc_vad = None
        
        # Performance tracking
        self.frames_processed = 0
        self.speech_segments_detected = 0
        self.false_positives = 0
        
        # Energy-based VAD parameters
        self.energy_threshold = 0.01
        self.energy_history = deque(maxlen=50)
        self.noise_floor = 0.001
        
        logger.info(f"VAD: Initialized with method '{method}' (sensitivity: {sensitivity})")
    
    def initialize(self) -> bool:
        """Initialize VAD system"""
        try:
            if self.method == "silero":
                return self._initialize_silero()
            elif self.method == "webrtc":
                return self._initialize_webrtc()
            elif self.method == "custom":
                return self._initialize_custom()
            else:
                logger.error(f"ERROR: Unknown VAD method: {self.method}")
                return False
                
        except Exception as e:
            logger.error(f"FATAL: VAD initialization failed: {e}")
            return False
    
    def _initialize_silero(self) -> bool:
        """Initialize Silero VAD model"""
        try:
            if not TORCH_AVAILABLE:
                logger.error("ERROR: PyTorch not available for Silero VAD")
                return False
            
            # Try to load Silero VAD model
            try:
                import torch
                self.silero_model, utils = torch.hub.load(
                    repo_or_dir='snakers4/silero-vad',
                    model='silero_vad',
                    force_reload=False,
                    onnx=False
                )
                self.silero_model.eval()
                logger.info("VAD: Silero VAD model loaded successfully")
                return True
                
            except Exception as e:
                logger.warning(f"WARNING: Failed to load Silero VAD: {e}")
                logger.info("VAD: Falling back to custom VAD")
                self.method = "custom"
                return self._initialize_custom()
                
        except Exception as e:
            logger.error(f"ERROR: Silero VAD initialization failed: {e}")
            return False
    
    def _initialize_webrtc(self) -> bool:
        """Initialize WebRTC VAD"""
        try:
            import webrtcvad
            self.webrtc_vad = webrtcvad.Vad()
            
            # Set aggressiveness (0-3, higher = more aggressive)
            aggressiveness = int(self.sensitivity * 3)
            self.webrtc_vad.set_mode(aggressiveness)
            
            logger.info(f"VAD: WebRTC VAD initialized (aggressiveness: {aggressiveness})")
            return True
            
        except ImportError:
            logger.warning("WARNING: webrtcvad not available, falling back to custom VAD")
            self.method = "custom"
            return self._initialize_custom()
        except Exception as e:
            logger.error(f"ERROR: WebRTC VAD initialization failed: {e}")
            return False
    
    def _initialize_custom(self) -> bool:
        """Initialize custom energy-based VAD"""
        try:
            # Calculate adaptive threshold based on sensitivity
            self.energy_threshold = 0.001 + (self.sensitivity * 0.05)
            logger.info(f"VAD: Custom VAD initialized (threshold: {self.energy_threshold:.4f})")
            return True
            
        except Exception as e:
            logger.error(f"ERROR: Custom VAD initialization failed: {e}")
            return False
    
    def run_processing_loop(self):
        """Main VAD processing loop"""
        logger.info("VAD: Processing loop started")
        
        while not self.should_stop:
            try:
                # Get audio data from input queue
                audio_result = self.input_queue.get_nowait()
                if audio_result is None:
                    time.sleep(0.001)  # Brief pause if no data
                    continue
                
                # Process audio for voice activity
                start_time = time.time()
                vad_result = self._process_audio_frame(audio_result)
                processing_time = time.time() - start_time
                
                # Send result if speech detected or speech ended
                if vad_result:
                    success = self.output_queue.put_nowait(vad_result)
                    if not success:
                        logger.warning("WARNING: VAD output queue full")
                
                # Update performance stats
                self.update_performance_stats(processing_time)
                self.frames_processed += 1
                
            except Exception as e:
                logger.error(f"ERROR: VAD processing error: {e}")
                time.sleep(0.001)
    
    def _process_audio_frame(self, audio_result: ProcessingResult) -> Optional[ProcessingResult]:
        """Process single audio frame for voice activity"""
        try:
            audio_data = audio_result.data['audio_data']
            timestamp = audio_result.timestamp
            
            # Detect voice activity
            is_voice = self._detect_voice_activity(audio_data)
            
            # Update VAD state machine
            return self._update_vad_state(audio_data, is_voice, timestamp)
            
        except Exception as e:
            logger.error(f"ERROR: Audio frame processing failed: {e}")
            return None
    
    def _detect_voice_activity(self, audio_data: np.ndarray) -> bool:
        """Detect voice activity in audio frame"""
        try:
            if self.method == "silero":
                return self._silero_detect(audio_data)
            elif self.method == "webrtc":
                return self._webrtc_detect(audio_data)
            else:  # custom
                return self._custom_detect(audio_data)
                
        except Exception as e:
            logger.error(f"ERROR: Voice detection failed: {e}")
            return False
    
    def _silero_detect(self, audio_data: np.ndarray) -> bool:
        """Silero VAD detection"""
        try:
            if self.silero_model is None:
                return self._custom_detect(audio_data)
            
            # Convert to tensor
            audio_tensor = torch.from_numpy(audio_data).float()
            
            # Get VAD probability
            with torch.no_grad():
                speech_prob = self.silero_model(audio_tensor, self.sample_rate).item()
            
            # Apply sensitivity threshold
            threshold = 1.0 - self.sensitivity
            return speech_prob > threshold
            
        except Exception as e:
            logger.debug(f"DEBUG: Silero detection error: {e}")
            return self._custom_detect(audio_data)
    
    def _webrtc_detect(self, audio_data: np.ndarray) -> bool:
        """WebRTC VAD detection"""
        try:
            if self.webrtc_vad is None:
                return self._custom_detect(audio_data)
            
            # Convert to 16-bit PCM
            audio_16bit = (audio_data * 32767).astype(np.int16)
            audio_bytes = audio_16bit.tobytes()
            
            # WebRTC VAD requires specific frame sizes
            frame_duration = 30  # ms
            frame_size = int(self.sample_rate * frame_duration / 1000)
            
            if len(audio_16bit) >= frame_size:
                frame_bytes = audio_bytes[:frame_size * 2]  # 2 bytes per sample
                return self.webrtc_vad.is_speech(frame_bytes, self.sample_rate)
            else:
                return False
                
        except Exception as e:
            logger.debug(f"DEBUG: WebRTC detection error: {e}")
            return self._custom_detect(audio_data)
    
    def _custom_detect(self, audio_data: np.ndarray) -> bool:
        """Custom energy-based VAD detection"""
        try:
            # Calculate RMS energy
            rms_energy = np.sqrt(np.mean(audio_data ** 2))
            
            # Update energy history for adaptive threshold
            self.energy_history.append(rms_energy)
            
            # Calculate adaptive noise floor
            if len(self.energy_history) >= 10:
                sorted_energies = sorted(self.energy_history)
                self.noise_floor = np.mean(sorted_energies[:len(sorted_energies)//4])  # Bottom 25%
            
            # Adaptive threshold based on noise floor and sensitivity
            adaptive_threshold = self.noise_floor + (self.sensitivity * 0.02)
            
            # Voice activity decision
            is_voice = rms_energy > adaptive_threshold

            # DEBUG: Log energy levels periodically
            if hasattr(self, '_debug_counter'):
                self._debug_counter += 1
            else:
                self._debug_counter = 0

            if self._debug_counter % 500 == 0:  # Log every 500 frames to reduce spam
                logger.info(f"VAD: RMS={rms_energy:.6f}, Threshold={adaptive_threshold:.6f}, Voice={is_voice}")

            # Additional checks for robustness
            if is_voice:
                # Check for spectral characteristics (simple high-frequency content check)
                fft = np.fft.fft(audio_data)
                freqs = np.fft.fftfreq(len(audio_data), 1/self.sample_rate)
                
                # Voice typically has energy in 300-3400 Hz range
                voice_band_mask = (freqs >= 300) & (freqs <= 3400)
                voice_energy = np.sum(np.abs(fft[voice_band_mask]))
                total_energy = np.sum(np.abs(fft))
                
                if total_energy > 0:
                    voice_ratio = voice_energy / total_energy
                    if voice_ratio < 0.1:  # Too little energy in voice band
                        is_voice = False
            
            return is_voice
            
        except Exception as e:
            logger.error(f"ERROR: Custom VAD detection failed: {e}")
            return False
    
    def _update_vad_state(self, audio_data: np.ndarray, is_voice: bool, timestamp: float) -> Optional[ProcessingResult]:
        """Update VAD state machine and return speech segments"""
        try:
            current_time = timestamp
            
            if is_voice:
                if not self.is_speaking:
                    # Speech started
                    self.is_speaking = True
                    self.speech_start_time = current_time
                    self.current_speech_frames = []
                    logger.info("VAD DEBUG: Speech started!")
                
                # Add to current speech
                self.current_speech_frames.append(audio_data)
                self.last_speech_time = current_time
                
            else:
                if self.is_speaking:
                    # Check if silence duration exceeds threshold
                    silence_duration = current_time - self.last_speech_time
                    
                    if silence_duration >= self.max_silence_duration:
                        # Speech ended
                        speech_duration = current_time - self.speech_start_time
                        
                        if speech_duration >= self.min_speech_duration:
                            # Valid speech segment
                            speech_audio = np.concatenate(self.current_speech_frames)
                            
                            result = ProcessingResult(
                                data={
                                    'audio_data': speech_audio,
                                    'sample_rate': self.sample_rate,
                                    'duration': speech_duration,
                                    'start_time': self.speech_start_time,
                                    'end_time': current_time,
                                    'vad_method': self.method
                                },
                                timestamp=current_time,
                                processing_time_ms=(current_time - self.speech_start_time) * 1000,
                                metadata={
                                    'source': 'vad',
                                    'speech_segment': True,
                                    'segment_id': self.speech_segments_detected
                                }
                            )
                            
                            self.speech_segments_detected += 1
                            self.is_speaking = False
                            self.current_speech_frames = []

                            logger.info(f"VAD DEBUG: Speech segment detected and sent to STT ({speech_duration:.2f}s)")
                            return result
                        else:
                            # Too short, discard
                            self.false_positives += 1
                            self.is_speaking = False
                            self.current_speech_frames = []
                            logger.debug("VAD: Speech segment too short, discarded")
            
            return None
            
        except Exception as e:
            logger.error(f"ERROR: VAD state update failed: {e}")
            return None
    
    def get_vad_stats(self) -> Dict[str, Any]:
        """Get VAD statistics"""
        return {
            'method': self.method,
            'sensitivity': self.sensitivity,
            'frames_processed': self.frames_processed,
            'speech_segments_detected': self.speech_segments_detected,
            'false_positives': self.false_positives,
            'is_currently_speaking': self.is_speaking,
            'current_speech_duration': time.time() - self.speech_start_time if self.is_speaking else 0,
            'energy_threshold': getattr(self, 'energy_threshold', 0),
            'noise_floor': getattr(self, 'noise_floor', 0)
        }
    
    def adjust_sensitivity(self, new_sensitivity: float):
        """Dynamically adjust VAD sensitivity"""
        self.sensitivity = max(0.0, min(1.0, new_sensitivity))
        
        if self.method == "custom":
            self.energy_threshold = 0.001 + (self.sensitivity * 0.05)
        elif self.method == "webrtc" and self.webrtc_vad:
            aggressiveness = int(self.sensitivity * 3)
            self.webrtc_vad.set_mode(aggressiveness)
        
        logger.info(f"VAD: Sensitivity adjusted to {self.sensitivity}")


# Test function
def test_vad():
    """Test VAD functionality"""
    print("Testing VAD...")
    
    # Create test queues
    input_queue = LockFreeQueue(maxsize=100, name="VADInputTest")
    output_queue = LockFreeQueue(maxsize=100, name="VADOutputTest")
    
    # Create VAD thread
    vad = VADThread(
        input_queue=input_queue,
        output_queue=output_queue,
        method="custom",
        sensitivity=0.5
    )
    
    # Test initialization
    if vad.initialize():
        print("SUCCESS: VAD initialized")
        
        # Test with synthetic audio
        sample_rate = 48000
        duration = 0.1  # 100ms
        samples = int(sample_rate * duration)
        
        # Silent audio
        silent_audio = np.zeros(samples, dtype=np.float32)
        
        # Noisy audio (simulated speech)
        noisy_audio = np.random.normal(0, 0.1, samples).astype(np.float32)
        
        # Test detection
        silent_result = vad._detect_voice_activity(silent_audio)
        noisy_result = vad._detect_voice_activity(noisy_audio)
        
        print(f"Silent audio detected as voice: {silent_result}")
        print(f"Noisy audio detected as voice: {noisy_result}")
        
        print("SUCCESS: VAD test completed")
    else:
        print("ERROR: VAD initialization failed")


if __name__ == "__main__":
    test_vad()
