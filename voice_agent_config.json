{"sample_rate": 48000, "channels": 1, "input_chunk_size": 512, "output_chunk_size": 512, "input_device_index": null, "output_device_index": null, "vad_method": "custom", "vad_sensitivity": 0.3, "vad_min_speech_duration": 0.15, "vad_max_silence_duration": 0.8, "stt_model_size": "tiny", "stt_device": "cpu", "stt_language": "en", "stt_confidence_threshold": -1.0, "ai_engine": "qwen_vl", "qwen_model": "qwen2.5vl:32b", "enable_vision": true, "enable_gpu_acceleration": true, "ollama_host": "localhost", "ollama_port": 11434, "max_response_tokens": 150, "temperature": 0.9, "context_memory_size": 20, "tts_engine": "pyttsx3", "tts_model_name": "pyttsx3", "tts_device": "cpu", "voice_preset": "female", "speech_rate": 1.4, "voice_rate": 200, "voice_volume": 0.9, "voice_id": null, "performance_monitoring": true, "log_level": "INFO", "enable_stats_logging": true, "stats_log_interval": 30.0, "target_latency_ms": 600.0, "enable_mixed_precision": true, "enable_model_quantization": true, "gpu_memory_fraction": 0.8, "enable_cuda_graphs": true, "circuit_breaker_enabled": true, "circuit_breaker_failure_threshold": 3, "circuit_breaker_recovery_timeout": 30.0, "enable_interruption": true, "enable_conversation_memory": true, "enable_audio_effects": false, "enable_noise_suppression": false, "preferred_models": ["qwen2.5vl:32b", "llama3.2:3b", "hermes3:8b"]}