"""
Speech-to-Text (STT) Processor
High-performance speech recognition with Faster-Whisper
"""

import numpy as np
import time
import logging
from typing import Optional, Dict, Any, List, Tuple
import threading
import tempfile
import os

try:
    from faster_whisper import WhisperModel
    FASTER_WHISPER_AVAILABLE = True
except ImportError:
    FASTER_WHISPER_AVAILABLE = False

try:
    import soundfile as sf
    SOUNDFILE_AVAILABLE = True
except ImportError:
    SOUNDFILE_AVAILABLE = False

from .threading_infrastructure import (
    HighPriorityThread,
    LockFreeQueue,
    ProcessingResult
)

logger = logging.getLogger(__name__)


class STTThread(HighPriorityThread):
    """Speech-to-Text processing thread"""
    
    def __init__(self,
                 input_queue: LockFreeQueue,
                 output_queue: LockFreeQueue,
                 model_size: str = "medium",
                 device: str = "cuda",
                 language: str = "en",
                 confidence_threshold: float = 0.3):
        
        super().__init__("STT", priority="high", target_fps=10)
        
        # Queues
        self.input_queue = input_queue
        self.output_queue = output_queue
        
        # STT configuration
        self.model_size = model_size
        self.device = device
        self.language = language
        self.confidence_threshold = confidence_threshold
        
        # Whisper model
        self.whisper_model = None
        self.model_loaded = False
        
        # Performance tracking
        self.transcriptions_processed = 0
        self.total_audio_duration = 0
        self.total_processing_time = 0
        self.failed_transcriptions = 0
        
        # Audio processing
        self.temp_dir = tempfile.mkdtemp(prefix="stt_")
        
        logger.info(f"STT: Initialized (model: {model_size}, device: {device}, language: {language})")

    def _check_cuda_availability(self) -> bool:
        """Check if CUDA is properly available for STT"""
        try:
            import ctranslate2

            # Check if CUDA devices are available
            cuda_device_count = ctranslate2.get_cuda_device_count()
            if cuda_device_count == 0:
                logger.warning("STT: No CUDA devices detected")
                return False

            # Try to get supported compute types for CUDA
            try:
                compute_types = ctranslate2.get_supported_compute_types("cuda")
                logger.info(f"STT: CUDA compute types available: {compute_types}")
                return True
            except Exception as e:
                logger.warning(f"STT: CUDA compute types check failed: {e}")
                return False

        except ImportError:
            logger.warning("STT: ctranslate2 not available for CUDA check")
            return False
        except Exception as e:
            logger.warning(f"STT: CUDA availability check failed: {e}")
            return False

    def initialize(self) -> bool:
        """Initialize STT system"""
        try:
            if not FASTER_WHISPER_AVAILABLE:
                logger.error("FATAL: faster-whisper not available")
                logger.error("Install with: pip install faster-whisper")
                return False
            
            if not SOUNDFILE_AVAILABLE:
                logger.warning("WARNING: soundfile not available, using numpy for audio")
                logger.warning("Install with: pip install soundfile")
            
            # Load Whisper model
            return self._load_whisper_model()
            
        except Exception as e:
            logger.error(f"FATAL: STT initialization failed: {e}")
            return False
    
    def _load_whisper_model(self) -> bool:
        """Load Faster-Whisper model with enhanced GPU acceleration"""
        try:
            logger.info(f"STT: Loading Whisper model '{self.model_size}' on {self.device}...")

            # Check CUDA availability first
            if self.device == "cuda":
                if not self._check_cuda_availability():
                    logger.warning("STT: CUDA issues detected, falling back to CPU")
                    self.device = "cpu"

            # Determine compute type based on device with optimizations
            if self.device == "cuda":
                # Use int8 quantization for better performance on GPU
                compute_type = "int8"  # Changed from float16 for better speed
                logger.info("STT: Using int8 quantization for GPU acceleration")
            else:
                compute_type = "int8"

            # Load model with GPU optimizations
            self.whisper_model = WhisperModel(
                self.model_size,
                device=self.device,
                compute_type=compute_type,
                download_root=None,
                local_files_only=False,
                # Additional GPU optimizations
                device_index=0,  # Use first GPU
                cpu_threads=4    # Limit CPU threads when using GPU
            )

            self.model_loaded = True
            logger.info(f"STT: Whisper model loaded successfully on {self.device}")

            # Test transcription to warm up model
            self._warm_up_model()

            return True

        except Exception as e:
            error_msg = str(e).lower()

            # Specific error handling for common CUDA/cuDNN issues
            if "cudnn" in error_msg:
                logger.error(f"STT: cuDNN error detected: {e}")
                logger.error("STT: cuDNN is required for GPU acceleration. Please install cuDNN 8 for CUDA 12.x")
                logger.error("STT: Download from: https://developer.nvidia.com/cudnn")
            elif "cuda" in error_msg:
                logger.error(f"STT: CUDA error detected: {e}")
                logger.error("STT: Please check CUDA installation and GPU drivers")
            else:
                logger.error(f"STT: Failed to load Whisper model: {e}")

            # Try fallback to CPU
            if self.device == "cuda":
                logger.info("STT: Attempting fallback to CPU...")
                self.device = "cpu"
                return self._load_whisper_model()

            return False
    
    def _warm_up_model(self):
        """Warm up model with dummy audio"""
        try:
            # Create dummy audio (1 second of silence)
            dummy_audio = np.zeros(16000, dtype=np.float32)
            
            # Transcribe dummy audio
            segments, info = self.whisper_model.transcribe(
                dummy_audio,
                language=self.language,
                beam_size=1,
                best_of=1,
                temperature=0.0
            )
            
            # Consume generator
            list(segments)
            
            logger.debug("STT: Model warmed up successfully")
            
        except Exception as e:
            logger.warning(f"WARNING: Model warm-up failed: {e}")
    
    def run_processing_loop(self):
        """Main STT processing loop"""
        logger.info("STT: Processing loop started")
        
        while not self.should_stop:
            try:
                # Get speech data from VAD
                speech_result = self.input_queue.get_nowait()
                if speech_result is None:
                    time.sleep(0.01)  # Brief pause if no data
                    continue

                # DEBUG: Log when we receive speech data
                logger.info(f"STT DEBUG: Received speech data for transcription")
                
                # Process speech for transcription
                start_time = time.time()
                stt_result = self._transcribe_speech(speech_result)
                processing_time = time.time() - start_time
                
                # Send result if transcription successful
                if stt_result:
                    success = self.output_queue.put_nowait(stt_result)
                    if not success:
                        logger.warning("WARNING: STT output queue full")
                
                # Update performance stats
                self.update_performance_stats(processing_time)
                
            except Exception as e:
                logger.error(f"ERROR: STT processing error: {e}")
                time.sleep(0.01)
    
    def _transcribe_speech(self, speech_result: ProcessingResult) -> Optional[ProcessingResult]:
        """Transcribe speech audio to text"""
        try:
            if not self.model_loaded:
                logger.error("ERROR: Whisper model not loaded")
                return None
            
            audio_data = speech_result.data['audio_data']
            sample_rate = speech_result.data.get('sample_rate', 48000)
            duration = speech_result.data.get('duration', len(audio_data) / sample_rate)
            
            # Prepare audio for Whisper
            processed_audio = self._prepare_audio_for_whisper(audio_data, sample_rate)
            
            # Transcribe with Whisper - optimized for real-time processing
            start_time = time.time()
            segments, info = self.whisper_model.transcribe(
                processed_audio,
                language=self.language,
                beam_size=1,  # Minimal beam size for streaming
                best_of=1,    # Single pass for speed
                temperature=0.0,
                condition_on_previous_text=False,
                vad_filter=False,  # We already did VAD upstream
                vad_parameters=None,
                # Streaming optimizations for ultra-low latency
                no_speech_threshold=0.5,  # Lower threshold for streaming
                log_prob_threshold=-1.0,
                compression_ratio_threshold=2.4,
                word_timestamps=False  # Disable for speed
            )
            
            # Extract transcription results
            transcription_text = ""
            segment_list = []
            total_confidence = 0
            segment_count = 0
            
            for segment in segments:
                segment_info = {
                    'start': segment.start,
                    'end': segment.end,
                    'text': segment.text.strip(),
                    'confidence': getattr(segment, 'avg_logprob', 0.0)
                }
                
                if segment_info['text']:  # Only include non-empty segments
                    segment_list.append(segment_info)
                    transcription_text += segment_info['text'] + " "
                    total_confidence += segment_info['confidence']
                    segment_count += 1
            
            transcription_text = transcription_text.strip()
            processing_time = time.time() - start_time
            
            # Calculate average confidence
            avg_confidence = total_confidence / max(1, segment_count)
            
            # Check confidence threshold
            if avg_confidence < self.confidence_threshold and transcription_text:
                logger.debug(f"STT: Low confidence transcription ({avg_confidence:.3f}): '{transcription_text}'")
            
            # DEBUG: Log transcription results
            logger.info(f"STT DEBUG: Transcription='{transcription_text}', Confidence={avg_confidence:.3f}, Threshold={self.confidence_threshold}")

            # Only return result if we have text and reasonable confidence
            if transcription_text and avg_confidence >= self.confidence_threshold:
                result = ProcessingResult(
                    data={
                        'text': transcription_text,
                        'confidence': avg_confidence,
                        'language': info.language,
                        'language_probability': info.language_probability,
                        'duration': duration,
                        'segments': segment_list,
                        'processing_time': processing_time,
                        'model_size': self.model_size,
                        'device': self.device
                    },
                    timestamp=time.time(),
                    processing_time_ms=processing_time * 1000,
                    metadata={
                        'source': 'stt',
                        'original_audio_duration': duration,
                        'segment_count': segment_count
                    }
                )
                
                self.transcriptions_processed += 1
                self.total_audio_duration += duration
                self.total_processing_time += processing_time
                
                logger.info(f"STT: '{transcription_text}' (conf: {avg_confidence:.3f}, {processing_time:.2f}s)")
                return result
            else:
                self.failed_transcriptions += 1
                logger.debug(f"STT: Transcription failed or low confidence")
                return None
                
        except Exception as e:
            logger.error(f"ERROR: Speech transcription failed: {e}")
            self.failed_transcriptions += 1
            return None
    
    def _prepare_audio_for_whisper(self, audio_data: np.ndarray, sample_rate: int) -> np.ndarray:
        """Prepare audio data for Whisper model"""
        try:
            # Ensure audio is float32
            if audio_data.dtype != np.float32:
                audio_data = audio_data.astype(np.float32)
            
            # Ensure mono audio
            if len(audio_data.shape) > 1:
                audio_data = np.mean(audio_data, axis=1)
            
            # Resample to 16kHz if needed (Whisper's expected sample rate)
            if sample_rate != 16000:
                audio_data = self._resample_audio(audio_data, sample_rate, 16000)
            
            # Normalize audio
            max_val = np.max(np.abs(audio_data))
            if max_val > 0:
                audio_data = audio_data / max_val * 0.95
            
            # Pad or trim to reasonable length (max 30 seconds for Whisper)
            max_samples = 16000 * 30  # 30 seconds at 16kHz
            if len(audio_data) > max_samples:
                audio_data = audio_data[:max_samples]
            elif len(audio_data) < 1600:  # Minimum 0.1 seconds
                # Pad with zeros
                padding = 1600 - len(audio_data)
                audio_data = np.pad(audio_data, (0, padding), mode='constant')
            
            return audio_data
            
        except Exception as e:
            logger.error(f"ERROR: Audio preparation failed: {e}")
            return audio_data
    
    def _resample_audio(self, audio_data: np.ndarray, orig_sr: int, target_sr: int) -> np.ndarray:
        """Simple audio resampling"""
        try:
            if orig_sr == target_sr:
                return audio_data
            
            # Simple linear interpolation resampling
            duration = len(audio_data) / orig_sr
            target_length = int(duration * target_sr)
            
            # Create new time indices
            orig_indices = np.linspace(0, len(audio_data) - 1, target_length)
            
            # Interpolate
            resampled = np.interp(orig_indices, np.arange(len(audio_data)), audio_data)
            
            return resampled.astype(np.float32)
            
        except Exception as e:
            logger.error(f"ERROR: Audio resampling failed: {e}")
            return audio_data
    
    def get_stt_stats(self) -> Dict[str, Any]:
        """Get STT statistics with GPU information"""
        avg_processing_time = 0
        avg_audio_duration = 0
        real_time_factor = 0

        if self.transcriptions_processed > 0:
            avg_processing_time = self.total_processing_time / self.transcriptions_processed
            avg_audio_duration = self.total_audio_duration / self.transcriptions_processed

            if avg_audio_duration > 0:
                real_time_factor = avg_processing_time / avg_audio_duration

        # Base stats
        stats = {
            'model_size': self.model_size,
            'device': self.device,
            'language': self.language,
            'model_loaded': self.model_loaded,
            'transcriptions_processed': self.transcriptions_processed,
            'failed_transcriptions': self.failed_transcriptions,
            'success_rate': self.transcriptions_processed / max(1, self.transcriptions_processed + self.failed_transcriptions),
            'avg_processing_time': avg_processing_time,
            'avg_audio_duration': avg_audio_duration,
            'real_time_factor': real_time_factor,
            'confidence_threshold': self.confidence_threshold
        }

        # Add GPU information
        gpu_info = self._get_gpu_info()
        stats.update(gpu_info)

        return stats

    def _get_gpu_info(self) -> Dict[str, Any]:
        """Get GPU information for STT"""
        gpu_info = {
            'cuda_available': False,
            'cuda_device_count': 0,
            'ctranslate2_cuda_support': False,
            'gpu_memory_used': 0
        }

        try:
            import ctranslate2
            gpu_info['ctranslate2_cuda_support'] = True
            gpu_info['cuda_device_count'] = ctranslate2.get_cuda_device_count()
            gpu_info['cuda_available'] = gpu_info['cuda_device_count'] > 0

            # Try to get compute types
            if gpu_info['cuda_available']:
                try:
                    compute_types = ctranslate2.get_supported_compute_types("cuda")
                    gpu_info['supported_compute_types'] = list(compute_types)
                except Exception as e:
                    gpu_info['compute_types_error'] = str(e)
                    gpu_info['supported_compute_types'] = []
        except ImportError:
            gpu_info['ctranslate2_available'] = False
        except Exception as e:
            gpu_info['gpu_error'] = str(e)

        return gpu_info
    
    def cleanup(self):
        """Cleanup STT resources"""
        try:
            # Clean up temporary directory
            if hasattr(self, 'temp_dir') and os.path.exists(self.temp_dir):
                import shutil
                shutil.rmtree(self.temp_dir, ignore_errors=True)
                logger.debug("STT: Temporary directory cleaned up")
                
        except Exception as e:
            logger.error(f"ERROR: STT cleanup error: {e}")


# Test function
def test_stt():
    """Test STT functionality"""
    print("Testing STT...")
    
    if not FASTER_WHISPER_AVAILABLE:
        print("ERROR: faster-whisper not available - cannot test STT")
        return
    
    # Create test queues
    input_queue = LockFreeQueue(maxsize=100, name="STTInputTest")
    output_queue = LockFreeQueue(maxsize=100, name="STTOutputTest")
    
    # Create STT thread
    stt = STTThread(
        input_queue=input_queue,
        output_queue=output_queue,
        model_size="tiny",  # Use tiny model for testing
        device="cpu"       # Use CPU for testing
    )
    
    # Test initialization
    if stt.initialize():
        print("SUCCESS: STT initialized")
        
        # Test with synthetic speech-like audio
        sample_rate = 16000
        duration = 2.0  # 2 seconds
        samples = int(sample_rate * duration)
        
        # Create synthetic "speech" (white noise with speech-like envelope)
        t = np.linspace(0, duration, samples)
        envelope = np.exp(-((t - duration/2) / (duration/4))**2)  # Gaussian envelope
        synthetic_speech = np.random.normal(0, 0.1, samples) * envelope
        synthetic_speech = synthetic_speech.astype(np.float32)
        
        # Test audio preparation
        prepared_audio = stt._prepare_audio_for_whisper(synthetic_speech, sample_rate)
        print(f"Audio prepared: {len(prepared_audio)} samples")
        
        stt.cleanup()
        print("SUCCESS: STT test completed")
    else:
        print("ERROR: STT initialization failed")


if __name__ == "__main__":
    test_stt()
